<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />

    <!-- Performance optimizations -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">

    <!-- Critical CSS for above-the-fold content -->
    <style>
      /* Reset and base styles */
      * { box-sizing: border-box; }
      body {
        margin: 0;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        line-height: 1.6;
        color: #333;
        background: #fff;
      }

      /* Critical navbar styles */
      .navbar {
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid #e5e7eb;
        transition: transform 0.3s ease;
      }

      .nav-container {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
      }

      .nav-name {
        font-weight: 700;
        font-size: 1.125rem;
        color: #3b82f6;
      }

      /* Loading state */
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        font-size: 1.125rem;
        color: #6b7280;
      }

      /* Skip link for accessibility */
      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #3b82f6;
        color: white;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1001;
        transition: top 0.3s;
      }

      .skip-link:focus {
        top: 6px;
      }
    </style>

    <!-- SEO Meta Tags -->
    <title>Nicholas Gerasimatos - Principal Technical Consultant | Cloud Architecture & AI/ML Integration</title>
    <meta name="description" content="Principal Technical Consultant specializing in Cloud Architecture, AI/ML Integration, and Platform Engineering. Expert in AWS, Azure, and modern development practices." />
    <meta name="keywords" content="Cloud Architecture, AI/ML, Platform Engineering, AWS, Azure, Technical Consultant, Software Development" />
    <meta name="author" content="Nicholas Gerasimatos" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://nicholas-gerasimatos.netlify.app/" />
    <meta property="og:title" content="Nicholas Gerasimatos - Principal Technical Consultant" />
    <meta property="og:description" content="Specializing in Cloud Architecture, AI/ML Integration, and Platform Engineering" />
    <meta property="og:image" content="https://nicholas-gerasimatos.netlify.app/SCR-20250626-jqdc.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://nicholas-gerasimatos.netlify.app/" />
    <meta property="twitter:title" content="Nicholas Gerasimatos - Principal Technical Consultant" />
    <meta property="twitter:description" content="Specializing in Cloud Architecture, AI/ML Integration, and Platform Engineering" />
    <meta property="twitter:image" content="https://nicholas-gerasimatos.netlify.app/SCR-20250626-jqdc.png" />

    <!-- LinkedIn -->
    <meta property="og:site_name" content="Nicholas Gerasimatos Resume" />
    <meta property="article:author" content="Nicholas Gerasimatos" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Nicholas Resume" />
    <link rel="apple-touch-icon" href="/vite.svg" />

    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Nicholas Gerasimatos",
      "jobTitle": "Principal Technical Consultant",
      "description": "Specializing in Cloud Architecture, AI/ML Integration, and Platform Engineering",
      "url": "https://nicholas-gerasimatos.netlify.app/",
      "sameAs": [
        "https://www.linkedin.com/in/nicholas-gerasimatos"
      ],
      "knowsAbout": [
        "Cloud Architecture",
        "AI/ML Integration",
        "Platform Engineering",
        "AWS",
        "Azure",
        "Software Development"
      ]
    }
    </script>

    <!-- Windows PWA Support -->
    <meta name="msapplication-TileColor" content="#3b82f6" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    <script type="module" crossorigin src="/assets/js/index-48e6ef1a.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/js/vendor-c28bf532.js">
    <link rel="modulepreload" crossorigin href="/assets/js/icons-2f7eca53.js">
    <link rel="stylesheet" href="/assets/css/index-c75a5c50.css">
  </head>
  <body>
    <div id="root">
      <div class="loading">Loading...</div>
    </div>
    
  </body>
</html>
