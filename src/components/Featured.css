.featured {
  background-color: var(--color-background);
  padding: var(--space-32) 0;
}

.container {
  max-width: var(--container-lg);
  margin: 0 auto;
  padding: 0 var(--space-24);
}

.section-title {
  color: var(--color-text);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  text-align: center;
  margin-bottom: var(--space-8);
}

.section-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  text-align: center;
  margin-bottom: var(--space-32);
}

.featured-grid {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: minmax(280px, 1fr);
  gap: var(--space-24);
  margin-bottom: var(--space-48);
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary) transparent;
  padding-bottom: var(--space-16);
}

/* Custom scrollbar for WebKit browsers */
.featured-grid::-webkit-scrollbar {
  height: 8px;
}

.featured-grid::-webkit-scrollbar-track {
  background: transparent;
}

.featured-grid::-webkit-scrollbar-thumb {
  background-color: var(--color-primary);
  border-radius: 4px;
}

.featured-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.featured-card:hover {
  transform: translateY(-4px);
  border-color: var(--color-primary);
}

.featured-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-16);
}

.featured-icon {
  font-size: var(--font-size-2xl);
}

.featured-badge {
  background-color: var(--color-primary);
  color: var(--color-text);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  text-transform: capitalize;
}

.featured-title {
  color: var(--color-text);
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-8);
}

.featured-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
  margin-bottom: var(--space-16);
}

.featured-highlight {
  background-color: var(--color-primary);
  border-radius: var(--radius-base);
  padding: var(--space-12);
  margin-bottom: var(--space-16);
}

.highlight-text {
  color: var(--color-text);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.featured-link {
  color: var(--color-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: color var(--duration-normal) var(--ease-standard);
}

.featured-link:hover {
  color: var(--color-primary-hover);
}

.social-proof {
  text-align: center;
  margin-top: var(--space-48);
  padding: 0 var(--space-24);
}

.social-proof-title {
  color: var(--color-text);
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-32);
  position: relative;
  display: inline-block;
}

.social-proof-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--color-primary);
  border-radius: 2px;
}

.company-logos-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-16);
}

.company-logos {
  margin: 0 -10px;
  padding: 20px 0;
}

.company-logo-wrapper {
  padding: 0 10px;
  outline: none;
}

.company-logo {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  padding: 15px;
  background: var(--color-surface);
  border-radius: var(--radius-md);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin: 0 auto;
  max-width: 180px;
  height: 70px;
  width: 100%;
}

.company-logo:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.logo-image {
  max-width: 100%;
  max-height: 50px;
  width: auto;
  height: auto;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
  display: block;
}

.company-logo-wrapper:hover .logo-image {
  filter: grayscale(0%);
}

/* Slick overrides */
.slick-slide {
  padding: 10px 0;
}

.slick-dots {
  position: relative;
  bottom: 0;
  margin-top: 20px;
}

.slick-dots li button:before {
  color: var(--color-primary);
  opacity: 0.3;
  font-size: 10px;
}

.slick-dots li.slick-active button:before {
  color: var(--color-primary);
  opacity: 1;
}

.slick-prev:before,
.slick-next:before {
  color: var(--color-primary);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.slick-prev:hover:before,
.slick-next:hover:before {
  opacity: 1;
}

.slick-prev {
  left: -25px;
}

.slick-next {
  right: -25px;
}

@media (max-width: 1024px) {
  .slick-prev {
    left: -15px;
  }
  
  .slick-next {
    right: -15px;
  }
}

@media (max-width: 768px) {
  .featured-grid {
    grid-template-columns: 1fr;
  }
  
  .company-logo {
    height: 60px;
    padding: 10px;
  }
  
  .logo-image {
    max-height: 40px;
  }
  
  .slick-prev,
  .slick-next {
    display: none !important;
  }
}