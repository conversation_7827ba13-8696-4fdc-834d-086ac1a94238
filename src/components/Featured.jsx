import React, { useRef, useState } from 'react';
import FadeInUp from './FadeInUp.jsx';
import resumeData from '../services/resumeData.jsx';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import './Featured.css';

const Featured = () => {
  const awards = resumeData.getAwards();
  const certificates = resumeData.getCertificates();
  const basics = resumeData.getBasics();

  // Featured content based on LinkedIn recommendations
  const featuredItems = [
    {
      type: 'achievement',
      title: 'Red Hat Innovation Award Winner',
      description: 'Recognized for outstanding achievements in open source with FICO\'s $10M+ analytics platform',
      icon: '🏆',
      link: '#awards',
      highlight: 'Reduced infrastructure costs by 20% and improved time to market by 70%'
    },
    {
      type: 'certification',
      title: 'AWS Solutions Architect',
      description: 'Current AWS certification demonstrating cloud architecture expertise',
      icon: '☁️',
      link: certificates.find(cert => cert.name.includes('AWS'))?.url || '#certificates',
      highlight: 'Valid through 2025'
    },
    {
      type: 'expertise',
      title: 'AI/ML Integration Specialist',
      description: 'Leading AI-driven solutions and automation implementations',
      icon: '🤖',
      link: '#experience',
      highlight: 'Implementing cutting-edge AI solutions at AHEAD'
    },
    {
      type: 'thought-leadership',
      title: 'OpenStack Speakers Bureau',
      description: 'Published articles and industry conference presentations',
      icon: '🎤',
      link: '#publications',
      highlight: 'Recognized thought leader in cloud technologies'
    }
  ];

  return (
    <section id="featured" className="featured" aria-labelledby="featured-title">
      <div className="container">
        <FadeInUp>
          <h2 id="featured-title" className="section-title">Featured Highlights</h2>
          <p className="section-subtitle">Key achievements and expertise that drive results</p>
        </FadeInUp>
        
        <div className="featured-grid">
          {featuredItems.map((item, index) => (
            <FadeInUp key={index} delay={0.1 * (index + 1)}>
              <div className="featured-card">
                <div className="featured-card-header">
                  <span className="featured-icon">{item.icon}</span>
                  <div className="featured-badge">{item.type}</div>
                </div>
                <h3 className="featured-title">{item.title}</h3>
                <p className="featured-description">{item.description}</p>
              </div>
            </FadeInUp>
          ))}
        </div>

        {/* Social Proof Section */}
        <FadeInUp delay={0.6}>
          <div className="social-proof">
            <h3 className="social-proof-title">Trusted by Industry Leaders</h3>
            <div className="company-logos-container">
              <Slider
                className="company-logos"
                dots={true}
                infinite={true}
                speed={500}
                slidesToShow={5}
                slidesToScroll={2}
                autoplay={true}
                autoplaySpeed={3000}
                pauseOnHover={true}
                responsive={[
                  {
                    breakpoint: 1024,
                    settings: {
                      slidesToShow: 4,
                      slidesToScroll: 2
                    }
                  },
                  {
                    breakpoint: 768,
                    settings: {
                      slidesToShow: 3,
                      slidesToScroll: 1
                    }
                  },
                  {
                    breakpoint: 480,
                    settings: {
                      slidesToShow: 2,
                      slidesToScroll: 1
                    }
                  }
                ]}
              >
                {[
                  { name: 'AWS', src: '/Logos/Amazon_Web_Services-Logo.wine.svg' },
                  { name: 'Red Hat', src: '/Logos/Red_Hat-Logo.wine.svg' },
                  { name: 'Microsoft', src: '/Logos/Microsoft-Logo.wine.svg' },
                  { name: 'FICO', src: '/Logos/FICO_logo.svg.png' },
                  { name: 'American Express', src: '/Logos/American_Express-Logo.wine.svg' },
                  { name: 'JPMorgan Chase', src: '/Logos/JPMorgan_Chase-Logo.wine.svg' },
                  { name: 'PayPal', src: '/Logos/PayPal-Logo.wine.svg' },
                  { name: 'Visa', src: '/Logos/Visa_Inc.-Logo.wine.svg' },
                  { name: 'Canon', src: '/Logos/Canon_Inc.-Logo.wine.svg' }
                ].map((company, index) => (
                  <div key={index} className="company-logo-wrapper">
                    <div className="company-logo">
                      <img 
                        src={company.src} 
                        alt={`${company.name} logo`} 
                        className="logo-image"
                        loading="lazy"
                        onError={(e) => {
                          console.error(`Failed to load logo for ${company.name}:`, company.src);
                          // Hide broken images
                          e.target.style.display = 'none';
                        }}
                        onLoad={(e) => {
                          console.log(`Successfully loaded logo for ${company.name}`);
                        }}
                      />
                    </div>
                  </div>
                ))}
              </Slider>
            </div>
          </div>
        </FadeInUp>
      </div>
    </section>
  );
};

export default Featured;